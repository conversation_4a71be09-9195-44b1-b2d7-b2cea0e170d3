<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Image Navigation Fix Test</h1>
        
        <div class="info">
            <strong>测试目的：</strong> 验证 useImageNavigation.ts 中的错误修复
        </div>

        <div id="status" class="status info">
            正在检查控制台错误...
        </div>

        <h3>控制台输出监控</h3>
        <div id="console-output" class="console-output">
            等待控制台消息...
        </div>

        <div>
            <button onclick="clearConsole()">清空控制台</button>
            <button onclick="checkErrors()">检查错误</button>
            <button onclick="navigateToPatient()">导航到患者详情</button>
        </div>

        <h3>错误检查结果</h3>
        <div id="error-results">
            <p>点击"检查错误"按钮开始检查...</p>
        </div>
    </div>

    <script>
        let consoleMessages = [];
        let errorCount = 0;
        let navigationErrors = 0;

        // 拦截控制台消息
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        const originalConsoleLog = console.log;

        console.error = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'error', message, time: new Date().toLocaleTimeString() });
            errorCount++;
            
            // 检查是否是导航相关错误
            if (message.includes('useImageNavigation') || message.includes('Cannot read properties of undefined')) {
                navigationErrors++;
            }
            
            updateConsoleOutput();
            originalConsoleError.apply(console, args);
        };

        console.warn = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'warn', message, time: new Date().toLocaleTimeString() });
            updateConsoleOutput();
            originalConsoleWarn.apply(console, args);
        };

        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('navigation') || message.includes('Canvas') || message.includes('enablePanning')) {
                consoleMessages.push({ type: 'log', message, time: new Date().toLocaleTimeString() });
                updateConsoleOutput();
            }
            originalConsoleLog.apply(console, args);
        };

        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            const recent = consoleMessages.slice(-20); // 只显示最近20条消息
            
            output.innerHTML = recent.map(msg => {
                const color = msg.type === 'error' ? '#dc3545' : 
                             msg.type === 'warn' ? '#ffc107' : '#28a745';
                return `<div style="color: ${color}; margin: 2px 0;">
                    [${msg.time}] ${msg.type.toUpperCase()}: ${msg.message}
                </div>`;
            }).join('');
            
            output.scrollTop = output.scrollHeight;
        }

        function clearConsole() {
            consoleMessages = [];
            errorCount = 0;
            navigationErrors = 0;
            updateConsoleOutput();
            document.getElementById('console-output').innerHTML = '控制台已清空...';
        }

        function checkErrors() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('error-results');
            
            if (navigationErrors === 0) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ 没有发现导航相关错误！修复成功！';
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ 发现 ${navigationErrors} 个导航相关错误`;
            }

            resultsDiv.innerHTML = `
                <h4>错误统计</h4>
                <ul>
                    <li>总错误数: ${errorCount}</li>
                    <li>导航相关错误: ${navigationErrors}</li>
                    <li>控制台消息总数: ${consoleMessages.length}</li>
                </ul>
                
                <h4>最近的错误消息</h4>
                ${consoleMessages.filter(msg => msg.type === 'error').slice(-5).map(msg => 
                    `<div style="background: #f8d7da; padding: 5px; margin: 5px 0; border-radius: 3px;">
                        [${msg.time}] ${msg.message}
                    </div>`
                ).join('') || '<p>没有错误消息</p>'}
            `;
        }

        function navigateToPatient() {
            // 模拟导航到患者详情页面
            window.location.href = '/#/patient/detail/123';
        }

        // 页面加载完成后开始监控
        window.addEventListener('load', function() {
            setTimeout(checkErrors, 2000); // 2秒后自动检查一次
        });

        // 定期更新状态
        setInterval(function() {
            const statusDiv = document.getElementById('status');
            if (navigationErrors === 0 && errorCount === 0) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ 运行正常，没有发现错误';
            } else if (navigationErrors > 0) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ 发现 ${navigationErrors} 个导航错误`;
            } else {
                statusDiv.className = 'status info';
                statusDiv.innerHTML = `⚠️ 发现 ${errorCount} 个一般错误`;
            }
        }, 5000);
    </script>
</body>
</html>
